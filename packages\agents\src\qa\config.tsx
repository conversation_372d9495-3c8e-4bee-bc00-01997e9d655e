import { AgentConfig, Role } from "@cscs-agent/core";
import { Copy, PromptTemplate, Rating } from "@cscs-agent/presets";

export const dynamicPageQaAgentConfig: AgentConfig = {
  name: "问答助手",
  code: "dynamic-page-qa",
  logo: "/assets/dynamic-page-qa-logo.png",
  welcome: "Hi，欢迎使用问答助手",
  description: "面向动态页面管理场景，提供自然语言交互式解答，提升配置效率",
  message: {
    blocks: {
      widgets: [],
    },
    slots: {
      footer: {
        widgets: [
          {
            code: "Copy",
            component: Copy,
            role: Role.AI,
          },
          {
            code: "Rating",
            component: Rating,
            role: Role.AI,
          },
          {
            code: "Copy",
            component: Copy,
            role: Role.HUMAN,
          },
        ],
      },
    },
  },
  prompts: [],
  commands: [
    {
      name: "",
      action: () => {},
    },
  ],
  suggestions: [],
  sender: {
    slots: {
      headerPanel: {
        title: "模板",
        widgets: [
          {
            code: "PromptTemplate",
            component: PromptTemplate,
          },
        ],
      },
      footer: {
        widgets: [],
      },
    },
    headerPanel: {
      enable: false,
    },
  },
  sidePanel: {
    render: {
      widgets: [],
    },
  },
  request: {
    chat: {
      url: "/chat/completion",
      headers: {},
      method: "POST",
    },
  },
};
