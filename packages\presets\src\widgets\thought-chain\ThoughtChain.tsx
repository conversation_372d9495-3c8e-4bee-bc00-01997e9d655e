import React, { useEffect, useMemo, useState } from "react";

import { MarkdownRender } from "@cscs-agent/core";

interface ThoughtChainItem {
  id: string;
  title: string;
  description: string;
  content?: string;
  status: "completed" | "loading" | "pending";
  expanded: boolean;
}

const ThoughtChain: React.FC<{ items: ThoughtChainItem[] }> = () => {
  const [steps, setSteps] = useState<ThoughtChainItem[]>([
    {
      id: "1",
      title: "企业关键字抽取",
      description: "提取企业关键字结果",
      content: "问题中包含的关键字有:[东吴证券'国投证券，中证信用，东吴证券，国投证券，中证信用",
      status: "completed",
      expanded: true,
    },
    {
      id: "2",
      title: "查询企业基本信息",
      description: "开始使用关键字查询",
      status: "completed",
      expanded: false,
    },
    {
      id: "3",
      title: "提交探查任务",
      description: "查询关联关系",
      content:
        "参数构建中...开始提交关系探查任务,模式为:多对多, 任务名称为:关系探查任务-20250704091721提交关系探查任务成功",
      status: "completed",
      expanded: true,
    },
    {
      id: "4",
      title: "获取任务结果",
      description: "查询任务结果",
      status: "completed",
      expanded: false,
    },
    {
      id: "5",
      title: "总结",
      description: "整理关系路径",
      status: "loading",
      expanded: false,
    },
  ]);

  const toggleStep = (stepId: string) => {
    setSteps(steps.map((step) => (step.id === stepId ? { ...step, expanded: !step.expanded } : step)));
  };

  const getStatusIcon = (status: ThoughtChainItem["status"]) => {
    switch (status) {
      case "completed":
        return (
          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        );
      case "loading":
        return (
          <svg className="w-4 h-4 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        );
      case "pending":
        return <div className="border-2 border-gray-300 rounded-full w-4 h-4" />;
    }
  };

  const getChevronIcon = (expanded: boolean) => {
    return (
      <svg
        className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${expanded ? "rotate-180" : ""}`}
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    );
  };

  return (
    <div className="bg-gray-50 mx-auto p-6 max-w-4xl min-h-screen">
      <div className="flex items-center gap-2 mb-6">
        <h1 className="font-medium text-blue-600 text-lg">思维链</h1>
        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
        </svg>
      </div>

      <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
        {steps.map((step, index) => (
          <div key={step.id} className="border-gray-100 border-b last:border-b-0">
            <button
              className="hover:bg-gray-50 focus:bg-gray-50 p-6 focus:outline-none w-full text-left transition-colors duration-150"
              onClick={() => toggleStep(step.id)}
            >
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 mt-1">{getStatusIcon(step.status)}</div>
                <div className="flex-1">
                  <div className="flex justify-between items-center w-full">
                    <h3 className="font-medium text-gray-900">{step.title}</h3>
                    {getChevronIcon(step.expanded)}
                  </div>
                  <p className="mt-1 text-gray-500 text-sm">{step.description}</p>
                </div>
              </div>
            </button>

            {step.expanded && step.content && (
              <div className="px-6 pb-6">
                <div className="bg-gray-50 ml-8 p-4 rounded-lg">
                  <p className="text-gray-700 text-sm leading-relaxed">{step.content}</p>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ThoughtChain;
