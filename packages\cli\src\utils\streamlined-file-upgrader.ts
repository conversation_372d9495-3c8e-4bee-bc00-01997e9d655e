/**
 * Streamlined file upgrader that combines comparison and modification in a single step
 * Uses LLM to directly compare and generate modified content, then shows diff before applying
 */

import { join, relative } from "path";

import chalk from "chalk";
import { diffLines, type Change } from "diff";
import inquirer from "inquirer";

import { Agent, run } from "@openai/agents";

import type { Diff<PERSON><PERSON>, Diff<PERSON><PERSON>ult, CompareFileItem, StreamlinedUpgradeResult, UserChoice } from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";
import { createModel } from "./model.js";

export class StreamlinedFileUpgrader {
  private fsManager: FileSystemManager;
  private agent?: Agent;
  private templatePath: string;

  constructor(templatePath: string) {
    this.fsManager = new FileSystemManager();
    this.templatePath = templatePath;
  }

  async createAgent(apiKey?: string) {
    // Initialize the LLM agent for streamlined upgrade only if API key is available
    if (apiKey ?? process.env.OPENAI_API_KEY) {
      this.agent = new Agent({
        name: "StreamlinedFileUpgrader",
        instructions: `You are an expert TypeScript/React developer.
          Your task is to upgrade project files to match the latest template while preserving user customizations.
          
          When upgrading files:
          1. Compare the current file content with the target template requirements, try to follow the template code as much as possible
          2. Preserve all user customizations and custom code that don't conflict with template requirements
          3. Apply only the necessary changes for compatibility and improvements
          4. Maintain code style, formatting and existing comments
          5. Ensure TypeScript syntax correctness
          6. Add necessary imports, remove unused imports
          7. Keep comments and documentation
          8. Focus on dependency updates, configuration changes, and structural improvements
          9. If project file is empty, use template file directly.
          10. Apply structural improvements from the template while preserving custom logic

          Always return the complete modified file content without explanations.
          If no changes are needed, return the original content unchanged.
          `,
        model: createModel(),
        modelSettings: {
          temperature: 0.0,
        },
      });
    }
  }

  /**
   * Upgrade multiple files using streamlined approach
   */
  async upgradeFiles(filesToUpgrade: CompareFileItem[]): Promise<StreamlinedUpgradeResult[]> {
    const results: StreamlinedUpgradeResult[] = [];
    await this.createAgent();

    for (const file of filesToUpgrade) {
      // filter files removed
      if (file.removed) {
        this.fsManager.deleteFile(file.filePath);
        Logger.info(`Deleted file: ${file.filePath}`);
        continue;
      }
      const result = await this.upgradeFile(file);
      if (result) {
        results.push(result);
      }
    }

    return results;
  }

  /**
   * Upgrade a single file using streamlined approach
   */
  private async upgradeFile(file: CompareFileItem): Promise<StreamlinedUpgradeResult | null> {
    // Resolve template file path
    const templateFilePath = this.resolveTemplatePath(file.filePath);

    try {
      let templateContent = "";
      if (this.fsManager.fileExists(templateFilePath)) {
        // File doesn't exist in template, it's removed.
        templateContent = await this.fsManager.readFile(templateFilePath);
      }

      // Get LLM-generated upgraded content in a single step
      const modifiedContent = await this.upgradeWithLLM(
        file.content,
        templateContent,
        file.filePath,
        templateFilePath,
        file.prompt,
      );

      // Generate diff between original and modified content
      const diffResult = this.generateDiff(file.filePath, file.content, modifiedContent);

      // Extract relative file path for result
      const relativePath = this.getRelativeFilePath(file.filePath);

      return {
        filePath: relativePath,
        originalContent: file.content,
        modifiedContent,
        diffResult,
        requiresUpdate: diffResult.hasChanges,
        updatePriority: this.determineUpdatePriority(file.content, modifiedContent),
      };
    } catch (error) {
      Logger.warning(`Failed to upgrade ${file.filePath}: ${error instanceof Error ? error.message : "Unknown error"}`);
      return null;
    }
  }

  /**
   * Streamlined LLM-driven upgrade that combines comparison and modification
   */
  private async upgradeWithLLM(
    projectContent: string,
    templateContent: string,
    projectFilePath: string,
    templateFilePath: string,
    fileUpdateGuidance: string,
  ): Promise<string> {
    // If no agent is available, return original content
    if (!this.agent) {
      Logger.warning("LLM upgrade not available without OpenAI API key. Keeping original content.");
      return projectContent;
    }

    const fileName = projectFilePath.split(/[/\\]/).pop() || "";
    const fileExtension = fileName.split(".").pop()?.toLowerCase() || "";

    // Create an intelligent prompt that handles both comparison and modification
    const prompt = `Compare the current project file with the template and generate the upgraded version.

PROJECT FILE (current version): ${fileName}
Path: ${projectFilePath}
\`\`\`${this.getLanguageFromExtension(fileExtension)}
${projectContent}
\`\`\`

TEMPLATE FILE (target version): ${fileName}
Path: ${templateFilePath}
\`\`\`${this.getLanguageFromExtension(fileExtension)}
${templateContent}
\`\`\`

UPGRADE INSTRUCTIONS:
1. Compare the project file with the template to identify what needs to be updated
2. Apply necessary changes to bring the project file up to the template standards
3. Focus on the specific guidance for this file: ${fileUpdateGuidance}

IMPORTANT: Return ONLY the complete upgraded file content. Do not include explanations, markdown formatting, or code blocks. If no changes are needed, return the original content exactly as provided.`;

    try {
      const result = await run(this.agent, prompt);

      if (!result.finalOutput || typeof result.finalOutput !== "string") {
        Logger.warning(`LLM upgrade failed for ${projectFilePath}, keeping original content`);
        return projectContent;
      }

      // Clean up the response - remove any markdown formatting if present
      let upgradedContent = result.finalOutput.trim();

      // Remove markdown code blocks if present
      const codeBlockMatch = upgradedContent.match(/```[\s\S]*?\n([\s\S]*?)```$/);
      if (codeBlockMatch) {
        upgradedContent = codeBlockMatch[1].trim();
      }

      return upgradedContent;
    } catch (error) {
      Logger.warning(
        `LLM upgrade failed for ${projectFilePath}: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      return projectContent;
    }
  }

  /**
   * Generate diff between original and modified content using diff package
   */
  private generateDiff(filePath: string, originalContent: string, modifiedContent: string): DiffResult {
    const changes = diffLines(originalContent.trim(), modifiedContent.trim());
    const resultDiffLines: DiffLine[] = [];
    let lineNumber = 1;

    for (const change of changes) {
      const lines = change.value.split("\n");
      // Remove the last empty line if it exists (from split)
      if (lines[lines.length - 1] === "") {
        lines.pop();
      }

      for (const line of lines) {
        if (change.added) {
          resultDiffLines.push({
            type: "added",
            content: line,
            lineNumber: lineNumber,
          });
        } else if (change.removed) {
          resultDiffLines.push({
            type: "removed",
            content: line,
            lineNumber: lineNumber,
          });
        } else {
          resultDiffLines.push({
            type: "context",
            content: line,
            lineNumber: lineNumber,
          });
        }
        lineNumber++;
      }
    }

    return {
      filePath,
      originalContent,
      modifiedContent,
      diffLines: resultDiffLines,
      hasChanges: changes.some((change: Change) => change.added || change.removed),
    };
  }

  /**
   * Display diff in a git-like format
   */
  displayDiff(diffResult: DiffResult): void {
    console.log(chalk.bold(`\n--- ${diffResult.filePath} (original)`));
    console.log(chalk.bold(`+++ ${diffResult.filePath} (modified)`));
    console.log(chalk.gray("@@ Changes @@"));

    for (const line of diffResult.diffLines) {
      switch (line.type) {
        case "added":
          console.log(chalk.green(`+ ${line.content}`));
          break;
        case "removed":
          console.log(chalk.red(`- ${line.content}`));
          break;
        case "context":
          // Only show context lines near changes for brevity
          if (this.isNearChange(line, diffResult.diffLines)) {
            console.log(chalk.gray(`  ${line.content}`));
          }
          break;
      }
    }
    console.log();
  }

  /**
   * Check if a context line is near a change (for displaying relevant context)
   */
  private isNearChange(line: DiffLine, allLines: DiffLine[]): boolean {
    const currentIndex = allLines.indexOf(line);
    const contextRange = 3; // Show 3 lines of context around changes

    for (
      let i = Math.max(0, currentIndex - contextRange);
      i <= Math.min(allLines.length - 1, currentIndex + contextRange);
      i++
    ) {
      if (allLines[i].type === "added" || allLines[i].type === "removed") {
        return true;
      }
    }

    return false;
  }

  /**
   * Prompt user for confirmation before applying changes
   */
  async promptUserForConfirmation(diffResult: DiffResult): Promise<UserChoice> {
    const { choice } = await inquirer.prompt([
      {
        type: "list",
        name: "choice",
        message: `Apply changes to ${diffResult.filePath}?`,
        choices: [
          { name: "Confirm and apply changes", value: "confirm" },
          { name: "Skip this file", value: "revert" },
          { name: "View full diff again", value: "view" },
        ],
        default: "confirm",
      },
    ]);

    return choice as UserChoice;
  }

  /**
   * Apply the upgraded content to the file
   */
  async applyUpgrade(
    projectPath: string,
    result: StreamlinedUpgradeResult,
    createBackup: boolean = true,
  ): Promise<void> {
    const fullPath = join(projectPath, result.filePath);

    try {
      // Create backup if requested
      const prevContent = await this.fsManager.readFile(fullPath);
      if (createBackup && prevContent !== "") {
        await this.fsManager.backupFile(fullPath);
      }

      // Write the modified content
      await this.fsManager.writeFile(fullPath, result.modifiedContent);
      Logger.info(`Applied upgrade to: ${result.filePath}`);
    } catch (error) {
      Logger.error(
        `Failed to apply upgrade to ${result.filePath}: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      throw error;
    }
  }

  /**
   * Resolve template file path based on project file path
   */
  private resolveTemplatePath(projectFilePath: string): string {
    // Handle root configuration files
    const relativePath = relative(process.cwd(), projectFilePath);

    return join(this.templatePath, relativePath);
  }

  /**
   * Get relative file path for display purposes
   */
  private getRelativeFilePath(projectFilePath: string): string {
    // Extract meaningful relative path
    return relative(process.cwd(), projectFilePath);
  }

  /**
   * Get appropriate language identifier for code blocks based on file extension
   */
  private getLanguageFromExtension(extension: string): string {
    const languageMap: Record<string, string> = {
      ts: "typescript",
      tsx: "typescript",
      js: "javascript",
      jsx: "javascript",
      json: "json",
      md: "markdown",
      yml: "yaml",
      yaml: "yaml",
      toml: "toml",
      css: "css",
      scss: "scss",
      html: "html",
      xml: "xml",
    };

    return languageMap[extension] || "";
  }

  /**
   * Determine update priority based on content changes
   */
  private determineUpdatePriority(
    originalContent: string,
    modifiedContent: string,
  ): "critical" | "recommended" | "optional" {
    // Simple heuristics to determine priority based on changes
    const hasPackageJsonChanges =
      originalContent.includes('"dependencies"') && modifiedContent.includes('"dependencies"');
    const hasImportChanges = originalContent.includes("import") !== modifiedContent.includes("import");
    const hasStructuralChanges = Math.abs(originalContent.split("\n").length - modifiedContent.split("\n").length) > 5;

    if (hasPackageJsonChanges || hasStructuralChanges) {
      return "critical";
    }

    if (hasImportChanges) {
      return "recommended";
    }

    return "optional";
  }
}
