import "@@/@cscs-agent/core/dist/agent-tailwind.css";
import "@@/@cscs-agent/presets/dist/presets-tailwind.css";
import "@@/@cscs-agent/agents/dist/agents-tailwind.css";
import "@@/@cscs-agent/icons/dist/icons.css";
import "virtual:app-dep-info";

import "./styles.css";

import dayjs from "dayjs";

import { createDefaultRouter, initApp } from "@cscs-agent/core";
import { AgentHome, Chat, Login, defaultAuthGuard } from "@cscs-agent/presets";

import { config } from "./agent-config";
import Home from "./pages/home";
import SoloMessage from "./pages/solo-message";

dayjs.locale("zh-cn");

const router = createDefaultRouter({
  pages: {
    home: {
      Component: Home,
    },
    chat: {
      Component: Chat,
    },
    agentHome: {
      Component: AgentHome,
    },
    login: {
      enable: true,
      Component: Login,
    },
  },
  authGuard: defaultAuthGuard,
  rootRoutes: [
    {
      path: "message/test-solo",
      Component: SoloMessage,
      auth: true,
    },
  ],
});

initApp({
  loginUrl: "/login",
  router,
  agentChatConfig: config,
}).then(() => {
  console.log("App initialized successfully");
});
